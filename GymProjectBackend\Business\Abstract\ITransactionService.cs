using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface ITransactionService
    {
        IDataResult<List<Transaction>> GetAll();
        IDataResult<List<Transaction>> GetByMemberId(int memberId);
        IResult Add(Transaction transaction);
        IDataResult<List<TransactionDetailDto>> GetTransactionsWithDetails();
        IResult UpdatePaymentStatus(int transactionId);
        IDataResult<List<TransactionDetailDto>> GetUnpaidTransactions(int memberId);
        IResult AddBulk(BulkTransactionDto bulkTransaction);
        IResult UpdateAllPaymentStatus(int memberId);
        IResult Delete(int transactionId);

    }
}
